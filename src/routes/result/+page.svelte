<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { userStore } from '$lib/stores/userStore.svelte';

	let mounted = $state(false);
	let faceSwappedImage = $state<string | null>(null);
	let isPrinting = $state(false);
	let printComplete = $state(false);

	// 4R print dimensions (in mm): 102 x 152 mm (4" x 6")
	const PRINT_WIDTH_MM = 102;
	const PRINT_HEIGHT_MM = 152;
	const PRINT_DPI = 300; // High quality printing

	onMount(() => {
		mounted = true;

		// Get the face-swapped image from URL params or store
		const imageUrl = $page.url.searchParams.get('image');
		console.log('Result page - Raw image URL from params:', imageUrl);

		if (imageUrl) {
			try {
				faceSwappedImage = decodeURIComponent(imageUrl);
				console.log('Result page - Decoded image URL:', faceSwappedImage);

				// Verify the image can be loaded
				const testImg = new Image();
				testImg.onload = () => {
					console.log('Result page - Image loaded successfully');
				};
				testImg.onerror = (error) => {
					console.error('Result page - Image failed to load:', error);
				};
				testImg.src = faceSwappedImage;

			} catch (error) {
				console.error('Result page - Error decoding image URL:', error);
				goto('/photo');
				return;
			}
		} else {
			console.log('Result page - No image URL provided, redirecting to photo page');
			// If no image provided, redirect back to photo page
			goto('/photo');
			return;
		}
	});

	async function handlePrint() {
		if (!faceSwappedImage) {
			console.log('No face swapped image available for printing');
			return;
		}

		console.log('Starting print process for image:', faceSwappedImage);
		isPrinting = true;

		try {
			// Create a new window for printing with 4R dimensions
			const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
			if (!printWindow) {
				throw new Error('Could not open print window - popup might be blocked');
			}

			console.log('Print window opened successfully');

			// Create the HTML content
			const htmlContent = `<!DOCTYPE html>
<html>
<head>
	<title>Print Photo - 4R Size</title>
	<style>
		@page {
			size: 4in 6in;
			margin: 0;
		}

		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			width: 4in;
			height: 6in;
			display: flex;
			align-items: center;
			justify-content: center;
			background: white;
			font-family: Arial, sans-serif;
		}

		.print-container {
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.print-image {
			max-width: 100%;
			max-height: 100%;
			object-fit: contain;
			border-radius: 8px;
		}

		.loading {
			text-align: center;
			color: #666;
		}

		@media print {
			body {
				-webkit-print-color-adjust: exact;
				print-color-adjust: exact;
			}
		}
	</style>
</head>
<body>
	<div class="print-container">
		<div class="loading">Loading image...</div>
		<img src="` + faceSwappedImage + z" alt="Face Swapped Photo" class="print-image" style="display: none;" />
	</div>
	<script>
		console.log('Print window script loaded');
		const img = document.querySelector('.print-image');
		const loading = document.querySelector('.loading');

		img.onload = function() {
			console.log('Print window - Image loaded successfully');
			loading.style.display = 'none';
			img.style.display = 'block';

			setTimeout(function() {
				console.log('Print window - Triggering print dialog');
				window.print();
			}, 1000);
		};

		img.onerror = function() {
			console.error('Print window - Image failed to load');
			loading.textContent = 'Failed to load image';
		};

		setTimeout(function() {
			if (img.style.display === 'none') {
				console.log('Print window - Fallback: showing print dialog anyway');
				loading.style.display = 'none';
				img.style.display = 'block';
				window.print();
			}
		}, 5000);
	</script>
</body>
</html>`;

			// Write the content to the print window
			printWindow.document.open();
			printWindow.document.write(htmlContent);
			printWindow.document.close();

			// Reset printing state after a delay
			setTimeout(() => {
				printComplete = true;
				isPrinting = false;
				console.log('Print process completed');
			}, 2000);

		} catch (error) {
			console.error('Print failed:', error);
			isPrinting = false;
			alert(`Printing failed: ${error.message}. Please check if popups are blocked.`);
		}
	}

	function handleDownload() {
		if (!faceSwappedImage) return;

		// Create download link
		const link = document.createElement('a');
		link.href = faceSwappedImage;
		link.download = `face-swap-result-${new Date().toISOString().slice(0, 10)}.png`;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}

	function handleStartOver() {
		// Reset user data and go back to start
		userStore.reset();
		goto('/');
	}

	function handleTakeAnother() {
		// Keep user data but go back to photo capture
		goto('/photo');
	}
</script>

<!-- Full-screen background -->
<div class="relative h-screen w-full overflow-hidden">
	<!-- Gradient Background -->
	<div class="absolute inset-0 bg-gradient-to-br from-green-900 via-blue-900 to-purple-900"></div>
	
	<!-- Overlay for better contrast -->
	<div class="absolute inset-0 bg-black/40"></div>

	<!-- Content -->
	<div class="relative z-10 flex h-full flex-col items-center justify-center px-12 text-center">
		<!-- Main Content Container -->
		<div class="max-w-4xl space-y-12" class:animate-fade-in={mounted}>
			<!-- Title -->
			<div class="space-y-6" class:animate-slide-up={mounted} style="animation-delay: 0.2s;">
				<h1 class="text-6xl md:text-7xl lg:text-8xl font-bold text-white drop-shadow-lg leading-tight">
					Your Character
				</h1>
				<p class="text-2xl md:text-3xl text-white/90 drop-shadow-md font-light">
					{#if printComplete}
						Photo printed successfully!
					{:else}
						Ready to print in 4R size (4" × 6")
					{/if}
				</p>
			</div>

			<!-- Result Image -->
			{#if faceSwappedImage}
				<div class="relative" class:animate-slide-up={mounted} style="animation-delay: 0.4s;">
					<div class="relative w-[400px] h-[600px] md:w-[500px] md:h-[750px] lg:w-[600px] lg:h-[900px] mx-auto bg-white rounded-3xl overflow-hidden shadow-2xl border-4 border-white/30 backdrop-blur-sm">
						<!-- 4R Aspect Ratio Container -->
						<div class="w-full h-full flex items-center justify-center p-4">
							<img
								src={faceSwappedImage}
								alt="Your face-swapped character"
								class="max-w-full max-h-full object-contain rounded-2xl shadow-lg"
								onload={() => console.log('Result page - Main image loaded successfully')}
								onerror={(e) => console.error('Result page - Main image failed to load:', e)}
							/>
						</div>

						<!-- Print Status Overlay -->
						{#if isPrinting}
							<div class="absolute inset-0 bg-black/50 flex items-center justify-center">
								<div class="bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 flex items-center gap-4">
									<div class="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
									<span class="text-white font-medium">Preparing to print...</span>
								</div>
							</div>
						{/if}
					</div>
				</div>
			{:else}
				<!-- No Image Fallback -->
				<div class="relative" class:animate-slide-up={mounted} style="animation-delay: 0.4s;">
					<div class="relative w-[400px] h-[600px] md:w-[500px] md:h-[750px] lg:w-[600px] lg:h-[900px] mx-auto bg-white/10 rounded-3xl overflow-hidden shadow-2xl border-4 border-white/30 backdrop-blur-sm">
						<div class="w-full h-full flex items-center justify-center p-4">
							<div class="text-center text-white/70">
								<div class="text-6xl mb-4">🖼️</div>
								<p class="text-xl">No image available</p>
								<p class="text-sm mt-2">faceSwappedImage: {faceSwappedImage || 'null'}</p>
								<button
									onclick={() => goto('/photo')}
									class="mt-4 px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
								>
									Back to Photo
								</button>
							</div>
						</div>
					</div>
				</div>
			{/if}

			<!-- Action Buttons -->
			<div class="flex flex-col md:flex-row gap-6 justify-center items-center" class:animate-slide-up={mounted} style="animation-delay: 0.6s;">
				<!-- Print Button -->
				<button
					onclick={handlePrint}
					disabled={isPrinting || !faceSwappedImage}
					class="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-full hover:scale-105 transition-all duration-300 font-semibold text-xl shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
				>
					{#if isPrinting}
						<div class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
						Printing...
					{:else}
						<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
						</svg>
						Print Photo (4R)
					{/if}
				</button>

				<!-- Download Button -->
				<button
					onclick={handleDownload}
					disabled={!faceSwappedImage}
					class="px-8 py-4 bg-white/20 backdrop-blur-sm border border-white/30 text-white rounded-full hover:bg-white/30 transition-all duration-300 font-semibold text-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
					</svg>
					Download
				</button>
			</div>

			<!-- Secondary Actions -->
			<div class="flex flex-col md:flex-row gap-4 justify-center items-center" class:animate-slide-up={mounted} style="animation-delay: 0.8s;">
				<button
					onclick={handleTakeAnother}
					class="px-6 py-3 text-white/80 hover:text-white transition-colors duration-300 text-lg underline"
				>
					Take Another Photo
				</button>
				
				<span class="text-white/50">•</span>
				
				<button
					onclick={handleStartOver}
					class="px-6 py-3 text-white/80 hover:text-white transition-colors duration-300 text-lg underline"
				>
					Start Over
				</button>
			</div>

			<!-- Print Info -->
			{#if !printComplete}
				<div class="text-center text-white/70" class:animate-slide-up={mounted} style="animation-delay: 1s;">
					<p class="text-lg mb-2">📏 Print Size: 4R (4" × 6" / 102 × 152 mm)</p>
					<p class="text-sm">High quality 300 DPI printing</p>
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	@keyframes fade-in {
		from { opacity: 0; }
		to { opacity: 1; }
	}

	@keyframes slide-up {
		from { 
			opacity: 0; 
			transform: translateY(30px); 
		}
		to { 
			opacity: 1; 
			transform: translateY(0); 
		}
	}

	.animate-fade-in {
		animation: fade-in 0.8s ease-out;
	}

	.animate-slide-up {
		animation: slide-up 0.6s ease-out;
	}
</style>
